<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '钱包',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging :paging-style="pageStyle" layout-only>
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          :bordered="false"
          custom-class="px-25rpx"
          left-arrow
          safe-area-inset-top
          @click-left="handleClickLeft"
        >
          <template #title>
            <text class="c-#333333 text-32rpx font500">钱包</text>
          </template>
        </wd-navbar>
      </wd-config-provider>
    </template>
    <view class="flex flex-col gap-16rpx px-60rpx mt-44rpx">
      <view class="card-box">
        <view class="card-top">
          <view class="card-company-name">重庆中誉易职网络信息技术有限公司</view>
          <view class="card-company-row">
            <view class="card-company-segment left">{{ codeSegments[0] }}</view>
            <view class="card-company-segment center">{{ codeSegments[1] }}</view>
            <view class="card-company-segment center">{{ codeSegments[2] }}</view>
            <view class="card-company-segment center">{{ codeSegments[3] }}</view>
            <view class="card-company-segment right">{{ codeSegments[4] }}</view>
          </view>
        </view>
        <view class="card-bottom">
          <view class="card-date">2025-5-25</view>
          <view class="card-balance">
            易币余额：
            <text class="card-balance-number">0.00</text>
            ￥
          </view>
        </view>
      </view>
    </view>
    <!-- 充值业务 -->
    <view class="recharge-box">
      <view class="recharge-options flex flex-row justify-between mb-40rpx">
        <view class="recharge-option">
          <view class="option-bonus option-bonus-disabled">赠送5%</view>
          <view class="option-amount">￥500</view>
        </view>
        <view class="recharge-option recharge-option-active">
          <view class="option-bonus">赠送8%</view>
          <view class="option-amount option-amount-active">￥1000</view>
        </view>
        <view class="recharge-option">
          <view class="option-bonus option-bonus-disabled">赠送10%</view>
          <view class="option-amount">￥2000</view>
        </view>
      </view>
      <view class="recharge-input-box mb-60rpx">
        <input class="recharge-input" placeholder="请输入充值金额" />
      </view>
      <view class="recharge-protocol mb-32rpx">
        付费即表示同意
        <!--        <text class="protocol-link">《易币说明》</text>-->
        <text class="protocol-link" @click="goProtocol">《易直聘增值服务协议》</text>
      </view>
      <view style="display: flex; justify-content: center">
        <button class="recharge-btn" disabled>暂未开放</button>
      </view>
    </view>

    <view class="recharge-operate-box">
      <view class="recharge-history-title">
        <view class="recharge-history-item" @click="goFundingDetails">
          <wd-img :height="19" :src="fundingDetails" :width="15" class="wd-img" />
          <text class="history-text">订单中心</text>
        </view>
        <view class="recharge-history-item" @click="goInvoicing">
          <wd-img :height="26" :src="Invoicing" :width="26" class="wd-img" />
          <text class="history-text">开发票</text>
        </view>
      </view>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import { type ConfigProviderThemeVars } from 'wot-design-uni'
import { usePayProp } from '@/sub_business/hooks/usePayProp'
import Invoicing from '@/static/mine/business/invoicing.png'
import fundingDetails from '@/static/mine/business/order-png.png'

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const { clearPayPropActive } = usePayProp()
const themeVars: ConfigProviderThemeVars = {
  navbarBackground: 'transparent',
  navbarArrowSize: '40rpx',
}

function handleClickLeft() {
  uni.navigateBack()
}

const companyCode = ref('91500107MAEH6YHQ42') // 这里用接口返回的字符串

const codeSegments = computed(() => {
  const str = companyCode.value || ''
  const total = 5
  const baseLen = Math.floor(str.length / total)
  const remainder = str.length % total
  const result = []
  let start = 0
  for (let i = 0; i < total; i++) {
    // 前 remainder 份多1个字符
    const segLen = baseLen + (i < remainder ? 1 : 0)
    result.push(str.slice(start, start + segLen))
    start += segLen
  }
  return result
})

// 协议
const goProtocol = () => {
  uni.navigateTo({
    url: '/setting/PrivacyAgreement/EnterpriseResourses',
  })
}

// 易币明细
const goFundingDetails = () => {
  uni.navigateTo({
    url: '/sub_business/pages/order/index',
  })
}

// 开发票
const goInvoicing = () => {
  uni.navigateTo({
    url: '/sub_business/pages/walletInvoice/Invoicing/index',
  })
}

onBeforeUnmount(() => {
  clearPayPropActive()
})
</script>

<style lang="scss" scoped>
.card-box {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
  min-height: 410rpx;
  padding: 20rpx 20rpx 58rpx 20rpx;
  color: #ffffff;
  background: url('/static/mine/business/card-info-bg.png') no-repeat center/cover;
  border-radius: 25rpx;
  box-shadow: 8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1);
}

.card-top {
  text-align: left;

  .card-company-name {
    font-size: 28rpx;
    font-weight: normal;
    color: #ffffff;
  }

  .card-company-row {
    display: flex;
    justify-content: space-between; // 保证间距均等
    width: 100%;
  }

  .card-company-segment {
    min-width: 0; // 防止内容溢出
    margin-top: 10rpx;
    font-size: 20rpx;
    color: #b0b0b0;
  }

  .card-company-segment.left {
    text-align: left;
  }

  .card-company-segment.right {
    text-align: right;
  }

  .card-company-segment.center {
    text-align: center;
  }
}

.card-bottom {
  text-align: right;

  .card-date {
    font-size: 24rpx;
    font-weight: normal;
    color: #ffffff;
  }

  .card-balance {
    font-size: 24rpx;
    color: #ffffff;

    .card-balance-number {
      font-size: 32rpx;
      font-weight: 500;
    }
  }
}

.recharge-box {
  padding: 32rpx 60rpx;
  margin-top: 64rpx;
  background: #ffffff;
  border-radius: 40rpx 40rpx 0 0;

  .recharge-options {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-bottom: 60rpx;
  }

  .recharge-option {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 180rpx;
    height: 135rpx;
    background: #fff;
    border: 2rpx solid #e5e5e5;
    border-radius: 24rpx;

    .option-bonus {
      position: absolute;
      top: 0;
      left: 50%;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80%;
      height: 38rpx;
      padding: 0 14rpx;
      clip-path: polygon(0% 0%, 100% 0%, 80% 100%, 20% 100%);
      font-size: 22rpx;
      line-height: 38rpx;
      color: #fff;
      background: #bfa16a;
      border-radius: 0 0 16rpx 16rpx;
      transform: translateX(-50%);
    }

    .option-bonus-disabled {
      color: #ffffff;
      background: #dcdada;
    }

    .option-amount {
      margin-top: 24rpx;
      font-size: 36rpx;
      font-weight: 500;
      color: #333;
    }

    .option-amount-active {
      color: #a28064;
    }
  }

  .recharge-option-active {
    border-color: #a28064;

    .option-bonus {
      color: #ffffff;
      background: #a28064;
      border-color: #a28064;
    }

    .option-amount {
      color: #bfa16a;
    }
  }

  .recharge-input-box {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin-bottom: 32rpx;

    .recharge-input {
      width: 70%;
      height: 80rpx;
      font-size: 28rpx;
      color: #888888;
      text-align: center;
      background: #ebebeb;
      border-radius: 16rpx;
    }
  }

  .recharge-protocol {
    margin-bottom: 32rpx;
    font-size: 22rpx;
    color: #888888;
    text-align: center;

    .protocol-link {
      margin: 0 8rpx;
      color: #3e78ff;
    }
  }

  .recharge-btn {
    width: 70%;
    height: 96rpx;
    margin-top: 16rpx;
    font-size: 32rpx;
    font-weight: normal;
    line-height: 96rpx;
    color: #fff;
    text-align: center;
    background: #797979;
    border: none;
    border-radius: 16rpx;
  }
}

.recharge-operate-box {
  min-height: 488rpx;
  padding: 32rpx 60rpx;
  margin-top: 32rpx;
  background: #ffffff;
}

.recharge-history-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.recharge-history-item {
  display: flex;
  align-items: center;
  padding: 16rpx 32rpx;
  border-radius: 12rpx;
}

.history-text {
  margin-left: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #000000;
}
</style>
