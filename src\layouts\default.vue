<template>
  <wd-config-provider :themeVars="themeVars" class="h-full">
    <slot />
    <wd-toast class="z-index-toast" />
    <wd-message-box class="z-index-message" />
  </wd-config-provider>
</template>

<script lang="ts" setup>
import type { ConfigProviderThemeVars } from 'wot-design-uni'
import { onMounted } from 'vue'
import { initToastConfig } from '@/utils/toast-config'

const themeVars: ConfigProviderThemeVars = {
  // colorTheme: 'red',
  buttonPrimaryBgColor: '#0096FF',
  tabbarHeight: '64px',
  // buttonPrimaryColor: '#07c160',
}

// 初始化弹框层级配置
onMounted(() => {
  initToastConfig()
})
</script>
