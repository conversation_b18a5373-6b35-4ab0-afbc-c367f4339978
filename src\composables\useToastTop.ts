/**
 * 确保 Toast 和 Message 在最上层显示的组合式函数
 */
import { useMessage, useToast } from 'wot-design-uni'
import { showToastTop } from '@/utils/toast-config'

export const useToastTop = () => {
  const message = useMessage()
  const toast = useToast()

  /**
   * 显示最高层级的成功提示
   */
  const showSuccessTop = (text: string, duration = 1500) => {
    showToastTop({
      title: text,
      icon: 'success',
      duration,
    })
  }

  /**
   * 显示最高层级的错误提示
   */
  const showErrorTop = (text: string, duration = 1500) => {
    showToastTop({
      title: text,
      icon: 'error',
      duration,
    })
  }

  /**
   * 显示最高层级的普通提示
   */
  const showInfoTop = (text: string, duration = 1500) => {
    showToastTop({
      title: text,
      icon: 'none',
      duration,
    })
  }

  /**
   * 显示最高层级的加载提示
   */
  const showLoadingTop = (text = '加载中...') => {
    showToastTop({
      title: text,
      icon: 'loading',
      duration: 0, // 不自动关闭
    })
  }

  /**
   * 隐藏 Toast
   */
  const hideToast = () => {
    uni.hideToast()
  }

  /**
   * 使用 wot-design-uni 的 message 组件（已设置最高层级）
   */
  const messageTop = {
    success: (text: string) => message.success(text),
    error: (text: string) => message.error(text),
    warning: (text: string) => message.warning(text),
    info: (text: string) => message.info(text),
  }

  /**
   * 使用 wot-design-uni 的 toast 组件（已设置最高层级）
   */
  const toastTop = {
    success: (text: string) => toast.success(text),
    error: (text: string) => toast.error(text),
    warning: (text: string) => toast.warning(text),
    info: (text: string) => toast.info(text),
    loading: (text: string) => toast.loading(text),
  }

  return {
    // uni.showToast 的最高层级版本
    showSuccessTop,
    showErrorTop,
    showInfoTop,
    showLoadingTop,
    hideToast,
    
    // wot-design-uni 组件的最高层级版本
    messageTop,
    toastTop,
    
    // 原始组件（保持兼容性）
    message,
    toast,
  }
}
