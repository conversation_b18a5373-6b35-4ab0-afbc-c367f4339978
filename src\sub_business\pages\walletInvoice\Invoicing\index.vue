<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '开发票',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging ref="pagingRef" v-model="pageData" :paging-style="pageStyle" @query="queryList">
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          :bordered="false"
          custom-class="px-25rpx"
          left-arrow
          safe-area-inset-top
          @click-left="handleClickLeft"
        >
          <template #title>
            <text class="c-#333333 text-32rpx font500">开发票</text>
          </template>
        </wd-navbar>
      </wd-config-provider>

      <view class="header-tabs">
        <view class="header-title">可开发票充值记录</view>
        <view class="header-action" @click="switchHeaderTab">开票记录</view>
      </view>

      <!-- 使用wotUI的tabs组件 -->
      <view class="tabs-wrapper">
        <wd-tabs
          v-model="activeTab"
          :line-width="40"
          color="#000000"
          custom-class="custom-tabs"
          inactive-color="#555555"
          line-height="2"
          slidable="always"
          @change="handleTabChange"
        >
          <wd-tab name="all" title="全部"></wd-tab>
          <wd-tab name="recharge" title="近三月"></wd-tab>
          <wd-tab name="usage" title="近一年"></wd-tab>
        </wd-tabs>
      </view>
    </template>

    <view class="recharge-details-box">
      <!-- 充值明细列表 -->
      <view v-for="(item, index) in pageData" :key="index" class="detail-item">
        <view class="item-content">
          <view class="item-left">
            <view class="item-title-row">
              <view class="item-title">{{ item.goodsDesc }}</view>
              <view class="item-time">{{ item.createTime }}</view>
            </view>
            <view class="item-amount">{{ formatAmount(item.totalAmount) }}元</view>
          </view>
          <view class="item-checkbox" @click="toggleItemSelection(item)">
            <view :class="{ checked: item.selected }" class="checkbox">
              <view v-if="item.selected" class="checkmark">
                <wd-icon color="#ffffff" name="check" size="20rpx" />
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </z-paging>

  <!-- 底部固定区域 -->
  <view class="bottom-section">
    <view class="bottom-info">
      <view class="total-info">
        <text class="total-label">合计:</text>
        <text class="total-amount">¥{{ formatAmount(totalAmountAll) }}</text>
      </view>
      <view class="total-count">{{ selectedCount }}笔充值记录</view>
    </view>
    <view class="next-button" @click="handleNext">
      <view class="next-button-text">下一步</view>
    </view>
  </view>

  <wd-toast />
  <wd-message-box />
</template>

<script lang="ts" setup>
import { type ConfigProviderThemeVars } from 'wot-design-uni'
import { usePayProp } from '@/sub_business/hooks/usePayProp'
import { payDealList } from '@/service/order'
import checkIcon from '@/sub_business/static/prop/check_icon.png'

const { pagingRef, pageInfo, pageData, pageStyle, pageSetInfo } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
    paddingBottom: '216rpx',
  },
})

const { clearPayPropActive } = usePayProp()
const themeVars: ConfigProviderThemeVars = {
  navbarBackground: 'transparent',
  navbarArrowSize: '40rpx',
}

function switchHeaderTab() {
  uni.navigateTo({
    url: '/sub_business/pages/walletInvoice/InvoiceRecord/index',
  })
}

const activeTab = ref('all')

function handleTabChange(name: string) {
  activeTab.value = name
  pagingRef.value?.reload()
}

const totalAmountAll = ref<any>(0.0)
const selectedCount = ref(0)
const params = ref({
  entity: {},
  orderBy: {},
  page: 1,
  size: 10,
})

function toggleItemSelection(item) {
  item.selected = !item.selected
  updateTotals()
}

function formatAmount(amount: number | string): string {
  const numAmount = Number(amount)
  if (isNaN(numAmount)) return '0.00'

  const yuan = (numAmount / 100).toFixed(2)
  return yuan
}

function updateTotals() {
  let count = 0
  let amount = 0

  pageData.value.forEach((item) => {
    if (item.selected) {
      count++
      amount += Number(item.totalAmount)
    }
  })

  selectedCount.value = count
  totalAmountAll.value = amount
}

const mockData = ref<any>([
  // {
  //   id: 90,
  //   title: '易币充值',
  //   createTime: '2024-12-20 10:57:54',
  //   amount: '0000',
  //   type: 'recharge',
  //   selected: false,
  // },
])

async function handleNext() {
  if (pageData.value === 0) {
    uni.showToast({
      title: '暂无可开发票充值记录',
      icon: 'none',
    })
    return
  }

  if (selectedCount.value === 0) {
    uni.showToast({
      title: '请至少选择一条记录',
      icon: 'none',
    })
    return
  }

  const selectedItems = pageData.value.filter((item) => item.selected)

  // 将数据存储到本地存储中
  uni.setStorageSync('invoiceSelectedData', {
    totalAmount: formatAmount(totalAmountAll.value),
    selectedItems,
  })

  uni.navigateTo({
    url: `/sub_business/pages/walletInvoice/InvoiceApplication/index`,
  })
}

function handleClickLeft() {
  uni.navigateBack()
}

const queryList = async (page, size) => {
  pageSetInfo(page, size)

  const res: any = await payDealList({ ...params.value, page: pageInfo.page, size: pageInfo.size })
  if (res.code === 0) {
    console.log(res, '111111111111111111111')
  }

  pagingRef.value.complete(res.data.list)
}

onLoad(() => {
  pagingRef.value?.reload()
})
onMounted(() => {
  pagingRef.value?.reload()
  setTimeout(() => {
    updateTotals()
  }, 500)
})

onBeforeUnmount(() => {
  clearPayPropActive()
})
</script>

<style lang="scss" scoped>
.header-tabs {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  padding: 40rpx 60rpx 0;
  background: transparent;
}

.header-title {
  font-size: 48rpx;
  font-weight: 500;
  line-height: 1.2;
  color: #000000;
}

.header-action {
  padding-bottom: 2rpx;
  padding-left: 40rpx;
  font-size: 28rpx;
  line-height: 1.2;
  color: #000000;
  cursor: pointer;
}

.tabs-wrapper {
  height: 148rpx;
  padding: 30rpx 60rpx;
  margin-top: 54rpx;
  font-size: 32rpx;
  background: #ffffff;
  border-bottom: 2rpx solid #d7d7d7;
  border-radius: 60rpx 60rpx 0 0;
}

:deep(.wd-tabs__container) {
  height: 0;
}

:deep(.custom-tabs) {
  .wd-tab {
    padding: 32rpx 0;
    font-size: 28rpx;
  }
}

:deep(.wd-tabs__nav-item) {
  font-size: 32rpx !important;
  transition: font-size 0.3s ease;
}

:deep(.wd-tabs__nav-item.is-active) {
  font-size: 32rpx !important;
}

.recharge-details-box {
  padding: 0 60rpx 96rpx;
  background: #ffffff;
}

:deep(.zp-empty-view-center) {
  background: #ffffff !important;
}

:deep(.zp-paging-container) {
  background: #ffffff !important;
}

.detail-item {
  padding: 0 0 0 0;
  background: #fff;
  border-bottom: 1rpx solid #ededed;
}

.item-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 36rpx 0 36rpx 0;
}

.item-left {
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: center;
}

.item-title-row {
  display: flex;
  align-items: baseline;
  margin-bottom: 8rpx;
}

.item-title {
  margin-right: 16rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #222;
}

.item-time {
  font-size: 24rpx;
  color: #888888;
}

.item-amount {
  margin-top: 0;
  font-size: 40rpx;
  font-weight: bold;
  color: #000;
}

.item-checkbox {
  display: flex;
  align-items: center;
  height: 100%;
  margin-left: 24rpx;
}

.checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  background: #fff;
  border: 2rpx solid #e0e0e0;
  border-radius: 50%;
  transition:
    border-color 0.2s,
    background 0.2s;

  &.checked {
    background: #007aff;
    border-color: #007aff;
  }
}

.checkmark {
  display: flex;
  align-items: center;
  justify-content: center;
}

.bottom-section {
  position: fixed;
  right: 32rpx;
  bottom: 32rpx;
  left: 32rpx;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 36rpx 40rpx;
  background: #f7f7f9;
  border-radius: 32rpx;
  box-shadow: none;
}

.bottom-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.total-info {
  display: flex;
  align-items: baseline;
}

.total-label {
  margin-right: 8rpx;
  font-size: 36rpx;
  font-weight: bold;
  color: #222;
}

.total-amount {
  font-size: 36rpx;
  font-weight: bold;
  color: #1677ff;
}

.total-count {
  margin-top: 8rpx;
  font-size: 26rpx;
  color: #bdbdbd;
}

.next-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 172rpx;
  height: 96rpx;
  margin-left: 32rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #fff;
  background-color: #1677ff;
  border-radius: 24rpx;
  box-shadow: none;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #888888;
}
</style>
