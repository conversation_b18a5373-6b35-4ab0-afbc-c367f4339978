/**
 * Toast 和 Message 组件层级覆盖样式
 * 确保弹框在最上层显示
 */

/* uni.showToast 最高层级 */
.uni-toast {
  z-index: 9999 !important;
  
  .uni-toast__content {
    z-index: 9999 !important;
  }
}

/* wot-design-uni message 组件最高层级 */
.wd-message-box {
  z-index: 9998 !important;
  
  &__wrapper {
    z-index: 9998 !important;
  }
  
  &__content {
    z-index: 9998 !important;
  }
}

/* wot-design-uni toast 组件最高层级 */
.wd-toast {
  z-index: 9997 !important;
  
  &__wrapper {
    z-index: 9997 !important;
  }
}

/* uni-popup 组件层级调整 */
.uni-popup {
  z-index: 2000 !important;
  
  &.fixforpc-z-index {
    z-index: 2000 !important;
  }
}

/* ChatUIKit 相关组件层级调整 */
.chat-wrap {
  .mask {
    z-index: 1200 !important;
  }
}

.popup-container {
  z-index: 1500 !important;
  
  .mask {
    z-index: 1500 !important;
  }
  
  .popup {
    z-index: 1501 !important;
  }
}

.modal-container {
  z-index: 1500 !important;
  
  .mask {
    z-index: 1500 !important;
  }
  
  .modal-content {
    z-index: 1501 !important;
  }
}

/* PopMenu 组件层级调整 */
.pop-menu {
  .mask {
    z-index: 1300 !important;
    
    .pop {
      z-index: 1301 !important;
    }
  }
}

/* 消息编辑遮罩层级 */
.msg-edit-mask {
  z-index: 1400 !important;
}

/* 确保所有弹框类组件都不会遮挡 Toast 和 Message */
.popup-root,
.uni-popup-wrapper,
.modal-wrapper,
.dialog-wrapper {
  z-index: 2000 !important;
  
  /* 但是要确保不超过 Toast 和 Message 的层级 */
  &.below-toast {
    z-index: 9000 !important;
  }
}
