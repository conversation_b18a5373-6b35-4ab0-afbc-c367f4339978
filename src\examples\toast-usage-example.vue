<template>
  <view class="toast-example-page">
    <view class="example-section">
      <text class="section-title">Toast 层级测试</text>
      
      <!-- 普通弹框 -->
      <wd-button @click="showModal" type="primary">显示普通弹框</wd-button>
      
      <!-- Toast 测试按钮 -->
      <view class="button-group">
        <wd-button @click="showUniToast" type="success">uni.showToast</wd-button>
        <wd-button @click="showUniToastTop" type="success">uni.showToast (最高层级)</wd-button>
      </view>
      
      <!-- Message 测试按钮 -->
      <view class="button-group">
        <wd-button @click="showMessage" type="warning">wot Message</wd-button>
        <wd-button @click="showMessageTop" type="warning">wot Message (最高层级)</wd-button>
      </view>
      
      <!-- Toast 组件测试按钮 -->
      <view class="button-group">
        <wd-button @click="showToast" type="info">wot Toast</wd-button>
        <wd-button @click="showToastTop" type="info">wot Toast (最高层级)</wd-button>
      </view>
    </view>

    <!-- 测试弹框 -->
    <wd-popup v-model="showPopup" position="center">
      <view class="popup-content">
        <text>这是一个测试弹框</text>
        <text>用于测试 Toast 和 Message 是否能在最上层显示</text>
        <wd-button @click="showPopup = false" type="primary">关闭</wd-button>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useMessage, useToast } from 'wot-design-uni'
import { useToastTop } from '@/composables/useToastTop'

const showPopup = ref(false)

// 使用原始组件
const message = useMessage()
const toast = useToast()

// 使用最高层级组件
const {
  showSuccessTop,
  showErrorTop,
  showInfoTop,
  messageTop,
  toastTop
} = useToastTop()

// 显示普通弹框
const showModal = () => {
  showPopup.value = true
}

// 测试 uni.showToast（可能被遮挡）
const showUniToast = () => {
  uni.showToast({
    title: '普通 Toast',
    icon: 'success'
  })
}

// 测试 uni.showToast 最高层级版本
const showUniToastTop = () => {
  showSuccessTop('最高层级 Toast')
}

// 测试 wot-design-uni Message（可能被遮挡）
const showMessage = () => {
  message.success('普通 Message')
}

// 测试 wot-design-uni Message 最高层级版本
const showMessageTop = () => {
  messageTop.success('最高层级 Message')
}

// 测试 wot-design-uni Toast（可能被遮挡）
const showToast = () => {
  toast.success('普通 wot Toast')
}

// 测试 wot-design-uni Toast 最高层级版本
const showToastTop = () => {
  toastTop.success('最高层级 wot Toast')
}
</script>

<style lang="scss" scoped>
.toast-example-page {
  padding: 40rpx;
}

.example-section {
  margin-bottom: 60rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 40rpx;
  display: block;
}

.button-group {
  display: flex;
  gap: 20rpx;
  margin: 20rpx 0;
  flex-wrap: wrap;
}

.popup-content {
  padding: 60rpx;
  text-align: center;
  
  text {
    display: block;
    margin-bottom: 30rpx;
    font-size: 28rpx;
  }
}
</style>
