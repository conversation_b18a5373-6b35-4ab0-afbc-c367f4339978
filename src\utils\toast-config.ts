/**
 * Toast 和 Message 弹框层级配置工具
 */

/**
 * 确保 uni.showToast 在最上层显示
 * @param options Toast 配置选项
 */
export const showToastTop = (options: UniApp.ShowToastOptions) => {
  // 设置最高层级的样式
  const style = document.createElement('style')
  style.innerHTML = `
    .uni-toast {
      z-index: 9999 !important;
    }
    .uni-toast .uni-toast__content {
      z-index: 9999 !important;
    }
  `
  document.head.appendChild(style)
  
  // 显示 Toast
  uni.showToast(options)
  
  // 清理样式（可选，避免样式累积）
  setTimeout(() => {
    document.head.removeChild(style)
  }, (options.duration || 1500) + 100)
}

/**
 * 为 wot-design-uni 的 message 组件设置最高层级
 */
export const setMessageTopLevel = () => {
  // 检查是否已经设置过
  if (document.getElementById('message-top-level-style')) {
    return
  }
  
  const style = document.createElement('style')
  style.id = 'message-top-level-style'
  style.innerHTML = `
    /* wot-design-uni message 组件最高层级 */
    .wd-message-box {
      z-index: 9998 !important;
    }
    .wd-message-box__wrapper {
      z-index: 9998 !important;
    }
    .wd-message-box__content {
      z-index: 9998 !important;
    }
    
    /* uni-popup 组件层级调整 */
    .uni-popup {
      z-index: 2000 !important;
    }
    
    /* ChatUIKit 遮罩层级调整 */
    .chat-wrap .mask {
      z-index: 1200 !important;
    }
    
    /* 其他弹框组件层级 */
    .popup-container {
      z-index: 1500 !important;
    }
    .modal-container {
      z-index: 1500 !important;
    }
  `
  document.head.appendChild(style)
}

/**
 * 在应用启动时初始化弹框层级
 */
export const initToastConfig = () => {
  // 设置 message 组件最高层级
  setMessageTopLevel()
  
  // 监听页面变化，确保样式持续生效
  if (typeof uni !== 'undefined') {
    uni.$on('onPageShow', () => {
      setMessageTopLevel()
    })
  }
}
